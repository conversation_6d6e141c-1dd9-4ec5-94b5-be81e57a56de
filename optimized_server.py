from fastmcp import FastMCP
import time

# 创建FastMCP服务器实例
mcp = FastMCP("OptimizedServer")

# 手动实现缓存
cache = {}
CACHE_SIZE = 128

# 使用装饰器定义工具函数
@mcp.tool()
def text_processor(text: str) -> str:
    """处理文本并优化token使用"""
    # 检查缓存
    if text in cache:
        return cache[text]
    
    # 截断长文本减少token使用
    processed = text[:50] + "..." if len(text) > 50 else text
    result = f"Processed at {int(time.time())}: {processed}"
    
    # 更新缓存
    if len(cache) >= CACHE_SIZE:
        # 移除最旧的条目
        cache.pop(next(iter(cache)))
    cache[text] = result
    
    return result

if __name__ == "__main__":
    # 直接运行FastMCP服务器
    mcp.run(host="0.0.0.0", port=8000)
