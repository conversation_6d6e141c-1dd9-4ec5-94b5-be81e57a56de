from fastmcp import FastMCP
import time
import hashlib
import re
from collections import OrderedDict
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CacheConfig:
    """缓存配置"""
    max_size: int = 256
    ttl_seconds: int = 3600  # 1小时过期
    enable_persistence: bool = False
    cache_file: str = "mcp_cache.json"

@dataclass
class OptimizationConfig:
    """优化配置"""
    max_text_length: int = 200
    enable_deduplication: bool = True
    batch_size: int = 10
    similarity_threshold: float = 0.8
    enable_smart_truncation: bool = True

class LRUCache:
    """LRU缓存实现，支持TTL"""

    def __init__(self, config: CacheConfig):
        self.config = config
        self.cache = OrderedDict()
        self.timestamps = {}

    def _is_expired(self, key: str) -> bool:
        """检查缓存项是否过期"""
        if key not in self.timestamps:
            return True
        return time.time() - self.timestamps[key] > self.config.ttl_seconds

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self.cache or self._is_expired(key):
            if key in self.cache:
                del self.cache[key]
                del self.timestamps[key]
            return None

        # 移动到末尾（最近使用）
        value = self.cache.pop(key)
        self.cache[key] = value
        return value

    def put(self, key: str, value: Any) -> None:
        """设置缓存值"""
        if key in self.cache:
            self.cache.pop(key)
        elif len(self.cache) >= self.config.max_size:
            # 移除最旧的项
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            del self.timestamps[oldest_key]

        self.cache[key] = value
        self.timestamps[key] = time.time()

    def clear_expired(self) -> int:
        """清理过期缓存项"""
        expired_keys = [k for k in self.cache.keys() if self._is_expired(k)]
        for key in expired_keys:
            del self.cache[key]
            del self.timestamps[key]
        return len(expired_keys)

class TextOptimizer:
    """文本优化器"""

    def __init__(self, config: OptimizationConfig):
        self.config = config

    def smart_truncate(self, text: str) -> str:
        """智能截断文本，保留重要信息"""
        if len(text) <= self.config.max_text_length:
            return text

        if not self.config.enable_smart_truncation:
            return text[:self.config.max_text_length] + "..."

        # 尝试在句子边界截断
        sentences = re.split(r'[.!?。！？]', text)
        result = ""
        for sentence in sentences:
            if len(result + sentence) <= self.config.max_text_length - 3:
                result += sentence + "."
            else:
                break

        if not result:
            # 如果没有找到合适的句子边界，在单词边界截断
            words = text.split()
            result = ""
            for word in words:
                if len(result + word) <= self.config.max_text_length - 3:
                    result += word + " "
                else:
                    break
            result = result.strip()

        return result + "..." if result else text[:self.config.max_text_length] + "..."

    def calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        # 简单的基于字符集合的相似度计算
        set1 = set(text1.lower().split())
        set2 = set(text2.lower().split())

        if not set1 and not set2:
            return 1.0
        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        return intersection / union if union > 0 else 0.0

    def generate_hash(self, text: str) -> str:
        """生成文本哈希"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

class OptimizedMCPServer:
    """优化的MCP服务器"""

    def __init__(self,
                 cache_config: Optional[CacheConfig] = None,
                 optimization_config: Optional[OptimizationConfig] = None):
        self.cache_config = cache_config or CacheConfig()
        self.optimization_config = optimization_config or OptimizationConfig()

        self.cache = LRUCache(self.cache_config)
        self.optimizer = TextOptimizer(self.optimization_config)
        self.mcp = FastMCP("OptimizedMCPServer")

        # 请求去重存储
        self.recent_requests = OrderedDict()
        self.request_history_size = 100

        self._register_tools()

        logger.info(f"MCP服务器初始化完成，缓存大小: {self.cache_config.max_size}")

    def _register_tools(self):
        """注册工具函数"""

        @self.mcp.tool()
        def process_text(text: str) -> Dict[str, Any]:
            """处理单个文本，优化token使用"""
            return self._process_single_text(text)

        @self.mcp.tool()
        def process_batch(texts: List[str]) -> Dict[str, Any]:
            """批量处理文本，减少请求次数"""
            return self._process_batch_texts(texts)

        @self.mcp.tool()
        def get_cache_stats() -> Dict[str, Any]:
            """获取缓存统计信息"""
            return self._get_cache_statistics()

        @self.mcp.tool()
        def clear_cache() -> Dict[str, str]:
            """清理缓存"""
            return self._clear_cache()

        @self.mcp.tool()
        def optimize_config(
            max_text_length: Optional[int] = None,
            enable_deduplication: Optional[bool] = None,
            batch_size: Optional[int] = None
        ) -> Dict[str, Any]:
            """动态调整优化配置"""
            return self._update_optimization_config(
                max_text_length, enable_deduplication, batch_size
            )

    def _process_single_text(self, text: str) -> Dict[str, Any]:
        """处理单个文本"""
        start_time = time.time()

        # 生成缓存键
        cache_key = self.optimizer.generate_hash(text)

        # 检查缓存
        cached_result = self.cache.get(cache_key)
        if cached_result:
            logger.info(f"缓存命中: {cache_key[:8]}...")
            return {
                "result": cached_result,
                "cached": True,
                "processing_time": time.time() - start_time,
                "original_length": len(text),
                "optimized_length": len(cached_result)
            }

        # 检查去重
        if self.optimization_config.enable_deduplication:
            similar_result = self._find_similar_request(text)
            if similar_result:
                logger.info(f"找到相似请求，复用结果")
                self.cache.put(cache_key, similar_result)
                return {
                    "result": similar_result,
                    "cached": False,
                    "deduplicated": True,
                    "processing_time": time.time() - start_time,
                    "original_length": len(text),
                    "optimized_length": len(similar_result)
                }

        # 优化文本
        optimized_text = self.optimizer.smart_truncate(text)

        # 模拟处理（实际应用中这里会调用LLM或其他处理逻辑）
        result = f"[{int(time.time())}] 处理结果: {optimized_text}"

        # 存储到缓存
        self.cache.put(cache_key, result)

        # 更新请求历史
        self._update_request_history(text, result)

        processing_time = time.time() - start_time
        logger.info(f"处理完成，耗时: {processing_time:.3f}s")

        return {
            "result": result,
            "cached": False,
            "deduplicated": False,
            "processing_time": processing_time,
            "original_length": len(text),
            "optimized_length": len(optimized_text),
            "compression_ratio": len(optimized_text) / len(text) if len(text) > 0 else 1.0
        }

    def _process_batch_texts(self, texts: List[str]) -> Dict[str, Any]:
        """批量处理文本"""
        start_time = time.time()
        results = []
        cache_hits = 0
        deduplication_hits = 0

        # 限制批处理大小
        if len(texts) > self.optimization_config.batch_size:
            logger.warning(f"批处理大小超限，截断到 {self.optimization_config.batch_size}")
            texts = texts[:self.optimization_config.batch_size]

        for i, text in enumerate(texts):
            try:
                result = self._process_single_text(text)
                results.append({
                    "index": i,
                    "success": True,
                    **result
                })

                if result.get("cached"):
                    cache_hits += 1
                if result.get("deduplicated"):
                    deduplication_hits += 1

            except Exception as e:
                logger.error(f"处理文本 {i} 时出错: {str(e)}")
                results.append({
                    "index": i,
                    "success": False,
                    "error": str(e)
                })

        total_time = time.time() - start_time

        return {
            "results": results,
            "summary": {
                "total_texts": len(texts),
                "successful": sum(1 for r in results if r.get("success")),
                "cache_hits": cache_hits,
                "deduplication_hits": deduplication_hits,
                "total_processing_time": total_time,
                "average_time_per_text": total_time / len(texts) if texts else 0
            }
        }

    def _find_similar_request(self, text: str) -> Optional[str]:
        """查找相似的历史请求"""
        for request_text, result in self.recent_requests.items():
            similarity = self.optimizer.calculate_similarity(text, request_text)
            if similarity >= self.optimization_config.similarity_threshold:
                logger.info(f"找到相似请求，相似度: {similarity:.2f}")
                return result
        return None

    def _update_request_history(self, text: str, result: str) -> None:
        """更新请求历史"""
        if len(self.recent_requests) >= self.request_history_size:
            # 移除最旧的请求
            self.recent_requests.popitem(last=False)

        self.recent_requests[text] = result

    def _get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        expired_count = self.cache.clear_expired()

        return {
            "cache_size": len(self.cache.cache),
            "max_cache_size": self.cache_config.max_size,
            "cache_usage_ratio": len(self.cache.cache) / self.cache_config.max_size,
            "expired_items_cleared": expired_count,
            "request_history_size": len(self.recent_requests),
            "ttl_seconds": self.cache_config.ttl_seconds,
            "optimization_config": asdict(self.optimization_config)
        }

    def _clear_cache(self) -> Dict[str, str]:
        """清理缓存"""
        cache_size = len(self.cache.cache)
        history_size = len(self.recent_requests)

        self.cache.cache.clear()
        self.cache.timestamps.clear()
        self.recent_requests.clear()

        logger.info(f"缓存已清理，移除了 {cache_size} 个缓存项和 {history_size} 个历史记录")

        return {
            "status": "success",
            "message": f"已清理 {cache_size} 个缓存项和 {history_size} 个历史记录"
        }

    def _update_optimization_config(self,
                                  max_text_length: Optional[int] = None,
                                  enable_deduplication: Optional[bool] = None,
                                  batch_size: Optional[int] = None) -> Dict[str, Any]:
        """更新优化配置"""
        old_config = asdict(self.optimization_config)

        if max_text_length is not None:
            self.optimization_config.max_text_length = max_text_length
        if enable_deduplication is not None:
            self.optimization_config.enable_deduplication = enable_deduplication
        if batch_size is not None:
            self.optimization_config.batch_size = batch_size

        new_config = asdict(self.optimization_config)

        logger.info("优化配置已更新")

        return {
            "status": "success",
            "old_config": old_config,
            "new_config": new_config
        }

    def run(self, host: str = "0.0.0.0", port: int = 8000):
        """启动服务器"""
        logger.info(f"启动优化的MCP服务器，地址: {host}:{port}")
        self.mcp.run(host=host, port=port)

# 创建服务器实例
def create_server(cache_size: int = 256,
                 max_text_length: int = 200,
                 enable_deduplication: bool = True) -> OptimizedMCPServer:
    """创建优化的MCP服务器实例"""
    cache_config = CacheConfig(max_size=cache_size)
    optimization_config = OptimizationConfig(
        max_text_length=max_text_length,
        enable_deduplication=enable_deduplication
    )

    return OptimizedMCPServer(cache_config, optimization_config)

if __name__ == "__main__":
    # 创建并启动服务器
    server = create_server()
    server.run()